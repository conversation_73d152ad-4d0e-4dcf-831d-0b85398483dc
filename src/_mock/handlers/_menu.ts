import { http, HttpResponse } from 'msw';

import type { MenuTree } from '@/types/entity';
import { MenuPermissionType, ResultStats, MenuApi } from '@/types/enum';

/**
 * Mock菜单数据
 */
const MOCK_MENU_TREE: MenuTree[] = [
    {
        id: '1',
        parentId: '',
        name: '首页',
        code: 'Home',
        type: MenuPermissionType.MENU,
        order: 1,
        icon: 'material-symbols:home-work-outline-rounded',
        path: '/workbench',
        component: 'Home',
        hidden: false,
        disabled: false,
    },
    {
        id: '2',
        parentId: '',
        name: '系统管理',
        code: 'system',
        type: MenuPermissionType.CATALOGUE,
        order: 2,
        icon: 'noto:blowfish',
        hidden: false,
        disabled: false,
        children: [
            {
                id: '2-1',
                parentId: '2',
                name: '用户管理',
                code: 'UserList',
                type: MenuPermissionType.MENU,
                order: 1,
                path: '/workbench',
                component: 'UserList',
                hidden: false,
                disabled: false,
            },
            {
                id: '2-2',
                parentId: '2',
                name: '用户详情',
                code: 'UserDetail',
                type: MenuPermissionType.MENU,
                order: 2,
                path: '/workbench',
                component: 'UserDetail',
                hidden: false,
                disabled: false,
            },
            {
                id: '2-3',
                parentId: '2',
                name: '仪表盘',
                code: 'Dashboard',
                type: MenuPermissionType.MENU,
                order: 3,
                path: '/workbench',
                component: 'Dashboard',
                hidden: false,
                disabled: false,
            },
        ],
    },
    {
        id: '3',
        parentId: '',
        name: '权限管理',
        code: 'permission',
        type: MenuPermissionType.CATALOGUE,
        order: 3,
        icon: 'material-symbols:admin-panel-settings-outline',
        hidden: false,
        disabled: false,
        children: [
            {
                id: '3-1',
                parentId: '3',
                name: '角色管理',
                code: 'role-management',
                type: MenuPermissionType.MENU,
                order: 1,
                path: '/workbench',
                component: 'RoleManagement',
                hidden: false,
                disabled: false,
            },
            {
                id: '3-2',
                parentId: '3',
                name: '菜单管理',
                code: 'menu-management',
                type: MenuPermissionType.MENU,
                order: 2,
                path: '/workbench',
                component: 'MenuManagement',
                hidden: false,
                disabled: false,
            },
        ],
    },
    {
        id: '4',
        parentId: '',
        name: '工具箱',
        code: 'tools',
        type: MenuPermissionType.CATALOGUE,
        order: 4,
        icon: 'material-symbols:battery-android-bolt-outline',
        hidden: false,
        disabled: false,
        children: [
            {
                id: '4-1',
                parentId: '4',
                name: '代码生成',
                code: 'code-generator',
                type: MenuPermissionType.MENU,
                order: 1,
                path: '/workbench',
                component: 'CodeGenerator',
                hidden: false,
                disabled: false,
            },
            {
                id: '4-2',
                parentId: '4',
                name: '系统监控',
                code: 'system-monitor',
                type: MenuPermissionType.MENU,
                order: 2,
                path: '/workbench',
                component: 'SystemMonitor',
                hidden: false,
                disabled: false,
            },
        ],
    },
];

/**
 * 获取菜单树的Mock处理器
 */
export const getMenuTree = http.get(MenuApi.MenuTree, async () => {
    console.log('MSW intercepted menu tree request');
    
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    return HttpResponse.json({
        status: ResultStats.SUCCESS,
        message: '获取菜单树成功',
        data: MOCK_MENU_TREE,
    });
});
